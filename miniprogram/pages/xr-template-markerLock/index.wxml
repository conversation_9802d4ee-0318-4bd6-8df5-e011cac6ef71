<view>
  <xr-template-markerLock
    disable-scroll
    id="main-frame"
    width="{{renderWidth}}"
    height="{{renderHeight}}"
    style="width:{{width}}px;height:{{height}}px;top:{{top}}px;left:{{left}}px;display:block;"
    initialVpsTracked="{{initialVpsTracked}}"
    isUp="{{isUp}}"
    unfinishedItemsCountRaw="{{unfinishedItemsCount}}"
    transformMatrix="{{transformMatrix}}"
    bindarInit="handleArInit"
    bindarTracked="handleArTracked"
    bindarLost="handleArLost"
    bindtrackerPositionReceived="handleTrackerPositionReceived"
    bindendSession="handleEndSession"
    bindcameraPoseTick="handleCameraPoseTick"
    bindoriginalCameraPoseTick="handleOriginalCameraPoseTick"
  />

  <!-- <text class="text-box-slam">{{originalPoseText}}</text>
  <text class="text-box-vps">{{cameraPoseText}}</text> -->

  <!-- Marker控制 -->
  <view class="xr-control" style="top: {{height}}px;">
    <view class="button-controls">
      <button bindtap="requestVpsEvent" data-project-id="2143" wx:if="{{arTracked}}">开始定位</button>
    <!-- <button bindtap="spawnCameraMesh" wx:if="{{arTracked}}">生成相机Pose</button> -->
    </view>
    <!-- <button bindtap="stopTracking">停止追踪</button> -->
    <!-- <view wx:for="{{markerList}}" wx:key="id" class="control-item {{item.isActive ? 'active' : ''}}" data-key="{{item.id}}">
    <view class="item-title">{{item.name}}</view>
    <view class="img-wrap">
      <image class="hint-img" src="{{item.markerImg}}">
      </image>
    </view>
  </view> -->
    <!-- <xr-agent-chat-viewer
    id="agent-chat-viewer"
    wx:if="{{initialVpsTracked}}"
    userPose="{{userPose}}"
    bindleaveIntent="handleLeaveIntent"
    /> -->
  </view>

  <view class="marker-wrap" wx:if="{{isVisitingItem}}" style="left: {{markerLeft}}%; top: {{markerTop}}%; width: {{markerWidth}}px; height: {{markerHeight}}px; margin-left: -{{markerWidth / 2}}px; margin-top: -{{markerHeight / 2}}px;">
    <image class="marker-img-lt" src="/assets/image/ar-box-border.png"></image>
    <image class="marker-img-lb" src="/assets/image/ar-box-border.png"></image>
    <image class="marker-img-rt" src="/assets/image/ar-box-border.png"></image>
    <image class="marker-img-rb" src="/assets/image/ar-box-border.png"></image>
  </view>
</view>